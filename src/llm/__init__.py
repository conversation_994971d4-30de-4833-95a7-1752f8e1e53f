import os
from dotenv import load_dotenv
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI

# Load environment variables
load_dotenv()


def create_llm_client():
    """Create LLM client with OpenRouter or OpenAI support"""
    
    # Try OpenRouter first (recommended for more model options and often better pricing)
    openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
    if openrouter_api_key and openrouter_api_key != "your-openrouter-api-key":
        # Get model from environment variable with fallback to default
        openrouter_model = os.getenv("OPENROUTER_MODEL", "openai/gpt-4o-mini")
        
        return ChatOpenAI(
            api_key=openrouter_api_key,
            base_url="https://openrouter.ai/api/v1",
            model=openrouter_model,
            timeout=30,
            max_retries=3,
            model_kwargs={
                "extra_headers": {
                    "HTTP-Referer": "https://github.com/51bitquant/ai-hedge-fund-crypto",  # Optional: for analytics
                    "X-Title": "AI Hedge Fund Crypto",  # Optional: for analytics
                }
            }
        )
    
    # Fallback to direct OpenAI
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if openai_api_key and openai_api_key != "your-openai-api-key":
        # Get model from environment variable with fallback to default
        openai_model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        
        return ChatOpenAI(
            api_key=openai_api_key,
            model=openai_model,
            timeout=30,
            max_retries=3
        )
    
    # If neither key is available, raise an error with helpful message
    raise ValueError(
        "No valid API key found. Please set either:\n"
        "- OPENROUTER_API_KEY (recommended - supports many models)\n"
        "- OPENAI_API_KEY (for direct OpenAI access)\n"
        "in your .env file."
    )


# Initialize the LLM client
openai_llm = create_llm_client()
json_parser = JsonOutputParser()

__all__ = ["openai_llm", "json_parser"]