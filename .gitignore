# Byte-compiled / cached files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
.env/
.env
.venv/

# Distribution / packaging
build/
dist/
*.egg-info/
pip-wheel-metadata/

# Logs and databases
*.log
*.sqlite3
*.db

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Pytest cache
.pytest_cache/

# MyPy cache
.mypy_cache/

# Coverage reports
.coverage
htmlcov/
*.cover
*.py,cover

# Editor / IDE files
.vscode/
.idea/
*.iml

# MacOS system files
.DS_Store

# Pycharm files
*.idea/

# Poetry files
poetry.lock

# Docker files (if using Docker)
docker-compose.override.yml

# Ignore local environment variables
.envrc
cache