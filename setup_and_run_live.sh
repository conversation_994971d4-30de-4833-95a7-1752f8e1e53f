#!/bin/zsh
# setup_and_run_live.sh
# This script sets up the environment and runs the project in live mode.

set -e

# 1. Check for uv, install if missing
if ! command -v uv &> /dev/null; then
  echo "uv not found. Installing..."
  curl -fsSL https://install.lunarvim.org/uv.sh | sh
fi

# 2. Create virtual environment if not exists
if [ ! -d ".venv" ]; then
  echo "Creating Python 3.12 virtual environment with uv..."
  uv venv --python 3.12
fi

# 3. Activate virtual environment
source .venv/bin/activate

# 4. Install dependencies
uv sync

# 5. Copy config/example files if not present
if [ ! -f ".env" ]; then
  cp ".env copy.example" .env
  echo ".env created. Please edit it to add your API keys (Binance and OpenRouter)."
fi
if [ ! -f "config.yaml" ]; then
  cp config.example.yaml config.yaml
  echo "config.yaml created."
fi

# 6. Set mode to live in config.yaml
if grep -q '^mode:' config.yaml; then
  sed -i '' 's/^mode:.*/mode: live/' config.yaml
else
  echo 'mode: live' >> config.yaml
fi

echo "\nSetup complete. Running in live mode...\n"

# 7. Run the main script
uv run main.py
