#!/usr/bin/env python3
"""
Test script for OpenRouter integration
Run this to verify your OpenRouter API key is working correctly.
"""

import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage

# Load environment variables
load_dotenv()

def test_openrouter():
    """Test OpenRouter connection with a simple query"""
    
    openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
    if not openrouter_api_key or openrouter_api_key == "your-openrouter-api-key":
        print("❌ OPENROUTER_API_KEY not found or not set in .env file")
        print("Please add your OpenRouter API key to the .env file:")
        print("OPENROUTER_API_KEY=your-actual-api-key")
        return False
    
    try:
        # Get model from environment variable with fallback to default
        openrouter_model = os.getenv("OPENROUTER_MODEL", "openai/gpt-4o-mini")
        
        # Create OpenRouter client
        client = ChatOpenAI(
            api_key=openrouter_api_key,
            base_url="https://openrouter.ai/api/v1",
            model=openrouter_model,
            timeout=30,
            max_retries=3,
            model_kwargs={
                "extra_headers": {
                    "HTTP-Referer": "https://github.com/51bitquant/ai-hedge-fund-crypto",
                    "X-Title": "AI Hedge Fund Crypto",
                }
            }
        )
        
        # Test with a simple query
        print(f"🔄 Testing OpenRouter connection with model: {openrouter_model}...")
        response = client.invoke([
            HumanMessage(content="Respond with exactly: 'OpenRouter connection successful!'")
        ])
        
        print(f"✅ Success! Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenRouter test failed: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Check your API key is correct at https://openrouter.ai/")
        print("2. Ensure you have credits in your OpenRouter account")
        print("3. Verify your internet connection")
        return False

def test_fallback_openai():
    """Test OpenAI fallback if available"""
    
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key or openai_api_key == "your-openai-api-key":
        print("ℹ️  OpenAI API key not configured (this is optional)")
        return False
    
    try:
        # Get model from environment variable with fallback to default
        openai_model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        
        # Create OpenAI client
        client = ChatOpenAI(
            api_key=openai_api_key,
            model=openai_model,
            timeout=30,
            max_retries=3
        )
        
        print("🔄 Testing OpenAI fallback connection...")
        response = client.invoke([
            HumanMessage(content="Respond with exactly: 'OpenAI connection successful!'")
        ])
        
        print(f"✅ OpenAI fallback works! Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI fallback test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing LLM API connections...\n")
    
    # Test OpenRouter (primary)
    openrouter_success = test_openrouter()
    print()
    
    # Test OpenAI (fallback)
    openai_success = test_fallback_openai()
    print()
    
    if openrouter_success:
        print("🎉 You're all set! OpenRouter is working correctly.")
        print("You can now run the trading system with: ./setup_and_run_live.sh")
    elif openai_success:
        print("🎉 OpenAI fallback is working. You can run the system.")
        print("Consider getting an OpenRouter key for more model options and better pricing.")
    else:
        print("⚠️  No working LLM API found. Please configure at least one:")
        print("- OpenRouter (recommended): https://openrouter.ai/")
        print("- OpenAI: https://platform.openai.com/")
