# Binance API keys (required for data access)
BINANCE_API_KEY=your-binance-api-key
BINANCE_API_SECRET=your-binance-api-secret

# For running LLMs via OpenRouter (supports many models including GPT-4, <PERSON>, etc.)
# Get your OpenRouter API key from https://openrouter.ai/
OPENROUTER_API_KEY=your-openrouter-api-key

# OpenRouter model to use (see https://openrouter.ai/models for available models)
# Popular options:
# - openai/gpt-4o-mini (fast, cheap, recommended for most trading)
# - openai/gpt-4o (more capable for complex analysis)
# - anthropic/claude-3-haiku (excellent reasoning, low cost)
# - anthropic/claude-3-sonnet (best reasoning, higher cost)
# - meta-llama/llama-3.1-8b-instruct (open source, very cheap)
OPENROUTER_MODEL=openai/gpt-4o-mini

# Optional: For direct OpenAI integration (if you prefer to use OpenAI directly)
# Get your OpenAI API key from https://platform.openai.com/
# OPENAI_API_KEY=your-openai-api-key
# OpenAI model to use when using direct OpenAI integration
OPENAI_MODEL=gpt-4o-mini