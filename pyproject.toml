[project]
name = "ai-hedge-fund-crypto"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.18",
    "colorama>=0.4.6",
    "dateparser>=1.2.1",
    "python-dotenv>=1.0.0",
    "langchain>=0.3.24",
    "langchain-openai>=0.3.16",
    "langgraph>=0.4.1",
    "matplotlib>=3.10.1",
    "pandas>=2.2.3",
    "pycryptodome>=3.22.0",
    "pydantic-settings>=2.9.1",
    "pyyaml>=6.0.2",
    "pyyml>=0.0.2",
    "requests>=2.32.3",
    "tabulate>=0.9.0",
    "websockets>=15.0.1",
    "websockets-proxy>=0.1.3 ; python_full_version >= '3.8'",
    "yarl>=1.20.0",
]

#[tool.setuptools]
#packages = ["src"]