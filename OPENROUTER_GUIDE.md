# OpenRouter Integration Guide

This project now supports **OpenRouter**, which provides access to multiple AI models including GPT-4, <PERSON>, <PERSON><PERSON><PERSON>, and many others through a single API. OpenRouter often offers better pricing and more model options compared to direct OpenAI access.

## Why OpenRouter?

- **More Models**: Access GPT-4, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and 100+ other models
- **Better Pricing**: Often 2-10x cheaper than direct API access
- **Single API**: One key for multiple model providers
- **Reliability**: Automatic failover between providers
- **Transparency**: Clear pricing and usage tracking

## Setup Instructions

### 1. Get Your OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for an account
3. Add credits to your account (as low as $5)
4. Generate an API key from your dashboard

### 2. Configure Your Environment

Add your OpenRouter API key and preferred model to the `.env` file:

```bash
# OpenRouter (recommended)
OPENROUTER_API_KEY=your-actual-openrouter-api-key
OPENROUTER_MODEL=openai/gpt-4o-mini

# OpenAI (optional fallback)
OPENAI_API_KEY=your-openai-api-key  # Optional
OPENAI_MODEL=gpt-4o-mini  # Optional
```

### 3. Choose Your Model

You can now specify your preferred model directly in the `.env` file using the `OPENROUTER_MODEL` variable. Popular options include:

```bash
# Fast and cheap (recommended for most trading)
OPENROUTER_MODEL=openai/gpt-4o-mini

# More capable for complex analysis
OPENROUTER_MODEL=openai/gpt-4o

# Anthropic Claude (excellent reasoning)
OPENROUTER_MODEL=anthropic/claude-3-haiku
OPENROUTER_MODEL=anthropic/claude-3-sonnet

# Open source alternatives
OPENROUTER_MODEL=meta-llama/llama-3.1-8b-instruct
OPENROUTER_MODEL=google/gemma-2-9b-it
```

See the full list of available models at [OpenRouter Models](https://openrouter.ai/models).

### 4. Test Your Setup

Run the test script to verify everything works:

```bash
python test_openrouter.py
```

The test will show which model is being used and confirm the connection is working.

### 5. Switch Models Anytime

You can easily switch between models by changing the `OPENROUTER_MODEL` value in your `.env` file and restarting your application. No code changes required!

## Model Recommendations

| Use Case                | Recommended Model                  | Cost      | Notes                                |
| ----------------------- | ---------------------------------- | --------- | ------------------------------------ |
| **Live Trading**        | `openai/gpt-4o-mini`               | Very Low  | Fast, reliable, cost-effective       |
| **Backtesting**         | `meta-llama/llama-3.1-8b-instruct` | Ultra Low | Open source, very cheap              |
| **Complex Analysis**    | `openai/gpt-4o`                    | Medium    | Best reasoning for complex decisions |
| **Conservative Choice** | `anthropic/claude-3-haiku`         | Low       | Excellent safety and reasoning       |

## Cost Comparison

Approximate costs per 1M tokens (as of 2024):

| Provider      | Model          | Input Cost | Output Cost |
| ------------- | -------------- | ---------- | ----------- |
| OpenRouter    | GPT-4o-mini    | $0.15      | $0.60       |
| OpenAI Direct | GPT-4o-mini    | $0.15      | $0.60       |
| OpenRouter    | Claude-3-Haiku | $0.25      | $1.25       |
| OpenRouter    | Llama-3.1-8B   | $0.05      | $0.05       |

## Troubleshooting

### "No valid API key found" Error

1. Check your `.env` file has the correct `OPENROUTER_API_KEY`
2. Verify the `OPENROUTER_MODEL` is set (defaults to `openai/gpt-4o-mini` if not specified)
3. Restart your application after adding the key
4. Verify the key works at [OpenRouter.ai](https://openrouter.ai/)

### "Insufficient credits" Error

1. Add credits to your OpenRouter account
2. Check your usage dashboard
3. Consider switching to a cheaper model

### Rate Limiting

If you hit rate limits:
1. Add delays between requests
2. Use a cheaper/faster model
3. Upgrade your OpenRouter plan

## Advanced Configuration

### Custom Headers

The integration includes analytics headers for OpenRouter:

```python
"extra_headers": {
    "HTTP-Referer": "https://github.com/51bitquant/ai-hedge-fund-crypto",
    "X-Title": "AI Hedge Fund Crypto",
}
```

### Model-Specific Parameters

Some models support additional parameters:

```python
model_kwargs={
    "temperature": 0.1,  # Lower = more deterministic
    "max_tokens": 1000,  # Limit response length
    "top_p": 0.9,       # Nucleus sampling
}
```

## Fallback to OpenAI

If you prefer direct OpenAI access, just comment out or remove the OpenRouter key from your `.env` file. The system will automatically fall back to using OpenAI directly.

## Support

- OpenRouter Documentation: https://openrouter.ai/docs
- OpenRouter Discord: https://discord.gg/openrouter
- Model Comparisons: https://openrouter.ai/models
